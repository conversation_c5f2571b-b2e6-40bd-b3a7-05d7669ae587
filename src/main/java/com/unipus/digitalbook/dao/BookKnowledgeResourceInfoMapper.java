package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceCompleteInfoPO;
import com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【book_knowledge_resource_info】的数据库操作Mapper
 * @createDate 2025-07-07 15:55:20
 * @Entity com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceInfo
 */
public interface BookKnowledgeResourceInfoMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BookKnowledgeResourceInfoPO record);

    int insertSelective(BookKnowledgeResourceInfoPO record);

    BookKnowledgeResourceInfoPO selectByPrimaryKey(Long id);

    List<BookKnowledgeResourceInfoPO> selectByPrimaryKeyList(@Param("ids") List<Long> ids);

    BookKnowledgeResourceInfoPO selectByBookIdAndSourceUrl(@Param("bookId") String bookId,@Param("sourceUrl") String sourceUrl);

    List<BookKnowledgeResourceInfoPO> selectListByThirdResourceIds(@Param("thirdIds") List<Long> thirdResourceIds, @Param("knowledgeId") String knowledgeId);

    int updateByPrimaryKeySelective(BookKnowledgeResourceInfoPO record);

    int updateByPrimaryKey(BookKnowledgeResourceInfoPO record);


    List<BookKnowledgeResourceCompleteInfoPO> selectCompleteInfoList(BookKnowledgeResourceCompleteInfoPO record);

}
