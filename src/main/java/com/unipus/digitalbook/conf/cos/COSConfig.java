package com.unipus.digitalbook.conf.cos;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.region.Region;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * 腾讯云cos config
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/8 16:12
 */
@Configuration
public class COSConfig {

    /**
     * 创建cosClient
     *
     * @param cosProperties COS属性对象，包含访问COS服务所需的密钥和区域信息
     * @return 返回一个配置好的COSClient对象
     */
    @Bean
    public COSClient cosClient(COSProperties cosProperties) {
        COSCredentials cred = new BasicCOSCredentials(cosProperties.getSecretId(), cosProperties.getSecretKey());
        Region region = new Region(cosProperties.getRegion());
        ClientConfig clientConfig = new ClientConfig(region);
        clientConfig.setHttpProtocol(HttpProtocol.https);
        return new COSClient(cred, clientConfig);
    }
}
